Make a site . Sample html design is in song.html. it has divs for home, single songs.


sql:

CREATE TABLE `saumya_sagaram` (
  `song_id` int(11) NOT NULL,
  `raw_text` longtext DEFAULT NULL,
  `section` varchar(255) DEFAULT '',
  `songtext` text DEFAULT NULL,
  `song_transliteration` text DEFAULT NULL,
  `translation` text DEFAULT NULL,
  `meaning` text DEFAULT NULL,
  `meaning_english` text DEFAULT NULL,
  `keywords` text DEFAULT NULL,
  `status` tinyint(4) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--


Table has 1000 songs. 
Home Page Manually link 1-50 songs. Mobile Resp.
song numbers only.  you can make numbers more accessible.

link each number to /song-1.html etc 
use .htaccess for make song.php to use it for this 

get when status = 6 

then display each field as correct place. make a good looking , seo frieldy etc.


--
mAke header footer as seperate php or make a layout.php and use it for home and this song pages. in future i may have addition pages to display all song numbers or categories or section wise etc..

this is very simple project dont confuse and complex.


--
Local file path: H:\xampp82\htdocs\Multiblity\2025\agastiya\saumya\site
This is on windows
This iapth is accessible on xampp here : http://localhost/Multiblity/2025/agastiya/saumya/site/
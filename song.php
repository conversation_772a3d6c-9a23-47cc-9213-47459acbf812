<?php
require_once 'config.php';
require_once 'layout.php';

// Helper function to clean up multiple consecutive line breaks
function cleanText($text) {
    if (empty($text)) return '';

    // First, escape HTML and convert newlines to <br>
    $cleaned = nl2br(htmlspecialchars($text));

    // Remove multiple consecutive <br> tags (2 or more)
    // This regex matches 2 or more consecutive <br> or <br/> or <br /> tags
    $cleaned = preg_replace('/(<br\s*\/?>\s*){2,}/i', '<br><br>', $cleaned);

    // Trim any leading/trailing <br> tags
    $cleaned = preg_replace('/^(<br\s*\/?>\s*)+|(<br\s*\/?>\s*)+$/i', '', $cleaned);

    return $cleaned;
}

// Get song ID from URL parameter
$songId = isset($_GET['id']) ? (int)$_GET['id'] : 1;

// Validate song ID
if ($songId < 1 || $songId > 1000) {
    $songId = 1;
}

try {
    // Fetch song data from database where status = 6
    $stmt = $pdo->prepare("SELECT * FROM saumya_sagaram WHERE song_id = ? AND status = 6");
    $stmt->execute([$songId]);
    $song = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$song) {
        // If song not found or status != 6, show error message
        $songNotFound = true;
    }
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

ob_start();
?>

<?php if (isset($error)): ?>
    <div class="alert alert-danger" role="alert">
        <h4 class="alert-heading">Error!</h4>
        <p><?php echo htmlspecialchars($error); ?></p>
    </div>
<?php elseif (isset($songNotFound)): ?>
    <div class="alert alert-warning" role="alert">
        <h4 class="alert-heading">Song Not Available</h4>
        <p>பாடல் #<?php echo $songId; ?> is not available or not yet published.</p>
        <hr>
        <p class="mb-0"><a href="index.php" class="btn btn-primary">Return to Home</a></p>
    </div>
<?php else: ?>
    <div class="song-detail p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <a href="index.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to List
            </a>
            <h2 class="mb-0">பாடல் <?php echo $songId; ?></h2>
        </div>

        <?php if (!empty($song['section'])): ?>
            <h3 class="text-center mb-4"><?php echo htmlspecialchars($song['section']); ?></h3>
        <?php endif; ?>
        <div class="section-divider"></div>

        <!-- Tamil Text -->
        <?php if (!empty($song['songtext'])): ?>
        <div class="mb-4">
            <h4><i class="fas fa-language me-2"></i>Tamil Text</h4>
            <div class="tamil-text">
                <?php echo cleanText($song['songtext']); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Transliteration -->
        <?php if (!empty($song['song_transliteration'])): ?>
        <div class="mb-4">
            <h4><i class="fas fa-spell-check me-2"></i>Transliteration</h4>
            <div class="transliteration">
                <?php echo cleanText($song['song_transliteration']); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Tamil Meaning -->
        <?php if (!empty($song['meaning'])): ?>
        <div class="meaning-section mb-4">
            <h4><i class="fas fa-book-open me-2"></i>Tamil Meaning</h4>
            <p><?php echo cleanText($song['meaning']); ?></p>
        </div>
        <?php endif; ?>

        <!-- English Meaning -->
        <?php if (!empty($song['meaning_english'])): ?>
        <div class="meaning-section mb-4">
            <h4><i class="fas fa-globe me-2"></i>English Meaning</h4>
            <p><?php echo cleanText($song['meaning_english']); ?></p>
        </div>
        <?php endif; ?>

        <!-- Translation -->
        <?php if (!empty($song['translation'])): ?>
        <div class="meaning-section mb-4">
            <h4><i class="fas fa-exchange-alt me-2"></i>Translation</h4>
            <p><?php echo cleanText($song['translation']); ?></p>
        </div>
        <?php endif; ?>

        <!-- Keywords -->
        <?php if (!empty($song['keywords'])): ?>
        <div class="mb-4">
            <h4><i class="fas fa-tags me-2"></i>Keywords</h4>
            <div>
                <?php 
                $keywords = explode(',', $song['keywords']);
                foreach ($keywords as $keyword): 
                    $keyword = trim($keyword);
                    if (!empty($keyword)):
                ?>
                    <span class="keyword-tag"><?php echo nl2br(htmlspecialchars($keyword)); ?></span>
                <?php 
                    endif;
                endforeach; 
                ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Navigation -->
        <div class="nav-buttons text-center">
            <?php if ($songId > 1): ?>
            <a href="song-<?php echo ($songId - 1); ?>.html" class="btn btn-nav me-3">
                <i class="fas fa-chevron-left me-2"></i>Previous
            </a>
            <?php endif; ?>
            
            <?php if ($songId < 50): ?>
            <a href="song-<?php echo ($songId + 1); ?>.html" class="btn btn-nav">
                Next<i class="fas fa-chevron-right ms-2"></i>
            </a>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<?php
$content = ob_get_clean();
$title = isset($song) && $song ? 'Song ' . $songId . ' - ' . (isset($song['section']) ? $song['section'] : 'Sowmiya Sagaram') : 'Song ' . $songId;
renderLayout($title, $content, 'song');
?>

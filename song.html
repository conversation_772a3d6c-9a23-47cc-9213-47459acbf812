<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sowmiya Sagaram - Tamil Sid<PERSON></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513; /* Saddle Brown - earthy, traditional */
            --secondary-color: #DAA520; /* Golden Rod - representing knowledge */
            --accent-color: #CD853F; /* Peru - warm terracotta */
            --text-primary: #2C1810; /* Dark brown */
            --text-secondary: #5D4E37; /* Dark olive brown */
            --bg-light: #FFF8DC; /* Cornsilk - warm, parchment-like */
            --bg-secondary: #F5F5DC; /* Beige */
            --border-color: #DEB887; /* Burlywood */
        }
        
        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }
        
        .sage-image {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid var(--secondary-color);
            object-fit: cover;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        
        /* Navigation */
        .navbar {
            background: rgba(139, 69, 19, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
        }
        
        .navbar-brand, .nav-link {
            color: white !important;
            font-weight: 500;
        }
        
        .navbar-brand {
            font-size: 1.1rem;
        }
        
        .navbar-toggler {
            border-color: rgba(218, 165, 32, 0.5);
        }
        
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        
        /* Main Content */
        .intro-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
            border: 2px solid var(--border-color);
        }
        
        .song-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            overflow: hidden;
            cursor: pointer;
        }
        
        .song-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.15);
            border-color: var(--secondary-color);
        }
        
        .song-number {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .song-title {
            color: var(--text-primary);
            font-weight: 600;
            margin: 0;
        }
        
        .song-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 0;
        }
        
        /* Song Detail Page */
        .song-detail {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid var(--border-color);
        }
        
        .tamil-text {
            font-family: 'Noto Sans Tamil', serif;
            font-size: 1.3rem;
            line-height: 1.8;
            color: var(--text-primary);
            background: var(--bg-light);
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid var(--secondary-color);
        }
        
        .transliteration {
            font-style: italic;
            color: var(--text-secondary);
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 3px solid var(--accent-color);
        }
        
        .meaning-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .keyword-tag {
            background: var(--secondary-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            margin: 0.2rem;
            display: inline-block;
        }
        
        /* Navigation Buttons */
        .nav-buttons {
            background: var(--bg-secondary);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 2rem;
        }
        
        .btn-nav {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-nav:hover {
            background: var(--accent-color);
            transform: translateY(-1px);
            color: white;
        }
        
        /* Footer */
        .footer {
            background: var(--primary-color);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        /* Decorative Elements */
        .section-divider {
            width: 100px;
            height: 3px;
            background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
            margin: 1.5rem auto;
            border-radius: 2px;
        }
        
        .om-symbol {
            font-size: 2rem;
            color: var(--secondary-color);
            text-align: center;
            margin: 1rem 0;
        }

        /* Quick Index */
        .quick-index {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid var(--border-color);
        }

        .index-number {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }

        .index-number:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
            background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 0.9rem;
            }
            
            .intro-section {
                margin: 1rem 0;
                padding: 1.5rem !important;
            }
            
            .song-card {
                margin-bottom: 0.8rem;
            }
            
            .song-number {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
            
            .song-title {
                font-size: 1rem;
            }
            
            .song-subtitle {
                font-size: 0.8rem;
            }
            
            .index-number {
                width: 40px;
                height: 40px;
                font-size: 0.9rem;
            }
            
            .quick-index {
                margin-top: 2rem;
            }

            .sage-image {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <!-- Header/Navigation -->
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container">
            <div class="d-flex align-items-center">
                <img src="agastiya.jpg" 
                     alt="Sage Agastiyar" class="sage-image me-3">
                <a class="navbar-brand flex-grow-1" href="#" onclick="showHome()">
                    <span class="d-none d-md-inline">சௌமிய சாகரம் - </span>Sowmiya Sagaram
                </a>
            </div>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="#" onclick="showHome()"><i class="fas fa-home me-1"></i>Home</a>
                    <a class="nav-link" href="#"><i class="fas fa-info-circle me-1"></i>About</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- Home Page -->
        <div id="home-page">
            <!-- Introduction -->
            <div class="intro-section p-4">
                <div class="om-symbol">ॐ</div>
                <h3 class="text-center mb-4">Welcome to Sowmiya Sagaram</h3>
                <div class="section-divider"></div>
                <p class="lead text-center">
					Discover the profound wisdom of Sage Agastiyar through this collection of Tamil Siddha songs - Agastiya Sowmiya Sagaram. Each verse carries the essence of ancient knowledge, spiritual insights, and the path to self-realization.
                </p>
                <p class="text-center">
In the sacred traditions of Tamil Siddha Yoga, words are never merely semantic containers. Each syllable is a flame, each verse a vessel of condensed tapas. Recognizing this, Agastya Kulapati Śrī Shakti Sumanan has brought forth a Dhyāna Urai — meanings revealed through deep meditative states — for these verses of Agastiyar.				
				</p>
				<p class="text-center">	
				His methodology of interpretation is at once devotional, philosophical, philological, and experiential. Rooted in the timeless transmission of nāda, mantra, and jñāna, his insights decode the hidden essence of the Siddha songs with razor-sharp academic clarity and spiritually sensitive depth.</p>
				<p class="text-center">	
				Sri Shakti Sumanan is a spiritual guide devoted to the Siddha tradition, known for his ability to unveil truths through inner contemplation. His expositions bridge devotion, philosophy, and practical wisdom, offering seekers a clear and transformative path into the timeless teachings of the Tamil Siddhas.
				<br><a href="https://srishaktisumanan.org/">Agastya Kulapati Śrī Shakti Sumanan</a>
				</p>
            </div>

            <!-- Song List and Quick Index -->
            <div class="row">
                <div class="col-lg-12">
                    <h3 class="mb-4"><i class="fas fa-list-ol me-2"></i>Quick Index</h3>
                    <div class="quick-index">
                        <div class="row g-2">
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(1)">1</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(2)">2</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(3)">3</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(4)">4</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(5)">5</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(6)">6</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(7)">7</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(8)">8</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(9)">9</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(10)">10</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(11)">11</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(12)">12</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(13)">13</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(14)">14</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(15)">15</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(16)">16</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(17)">17</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(18)">18</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(19)">19</div>
                            </div>
                            <div class="col-3 col-sm-2 col-lg-4">
                                <div class="index-number" onclick="showSong(20)">20</div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <small class="text-muted">Click on any number to go to song</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="song-detail" >
            <div class="song-detail p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <button class="btn btn-outline-primary" onclick="showHome()">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </button>
                    <h2 class="mb-0">Song <span id="current-song-number">1</span></h2>
                </div>

                <h3 class="text-center mb-4" id="song-title-detail">கடவுள் வாழ்த்து</h3>
                <div class="section-divider"></div>

                <!-- Tamil Text -->
                <div class="mb-4">
                    <h4><i class="fas fa-language me-2"></i>Tamil Text</h4>
                    <div class="tamil-text" id="tamil-text">
                        வான்முகிலே வாழி வாழி<br>
                        மாமுனியே வாழி வாழி<br>
                        ஞான ஒளியே வாழி வாழி<br>
                        நாதனே வாழி வாழி
                    </div>
                </div>

                <!-- Transliteration -->
                <div class="mb-4">
                    <h4><i class="fas fa-spell-check me-2"></i>Transliteration</h4>
                    <div class="transliteration" id="transliteration">
                        Vaan mugilē vāzi vāzi<br>
                        Mā muniyē vāzi vāzi<br>
                        Nyāna oḷiyē vāzi vāzi<br>
                        Nādanē vāzi vāzi
                    </div>
                </div>

                <!-- Tamil Meaning -->
                <div class="meaning-section mb-4">
                    <h4><i class="fas fa-book-open me-2"></i>Tamil Meaning</h4>
                    <p id="tamil-meaning">
                        ஆகாயத்தில் மிதக்கும் மேகமே வாழ்க! பெரிய முனிவரே வாழ்க! 
                        ஞான ஒளியே வாழ்க! தலைவனே வாழ்க! என்று தெய்வீக சக்திகளுக்கு 
                        வணக்கம் செலுத்தும் துதி பாடல்.
                    </p>
                </div>

                <!-- English Meaning -->
                <div class="meaning-section mb-4">
                    <h4><i class="fas fa-globe me-2"></i>English Meaning</h4>
                    <p id="english-meaning">
                        This is an invocational verse offering salutations to divine forces: 
                        "May the clouds in the sky prosper! May the great sage prosper! 
                        May the light of wisdom prosper! May the Lord prosper!" 
                        It establishes reverence for natural elements, spiritual wisdom, and divine presence.
                    </p>
                </div>

                <!-- Keywords -->
                <div class="mb-4">
                    <h4><i class="fas fa-tags me-2"></i>Keywords</h4>
                    <div id="keywords">
                        <span class="keyword-tag">வான்முகில் (Sky-cloud)</span>
                        <span class="keyword-tag">முனி (Sage)</span>
                        <span class="keyword-tag">ஞானம் (Wisdom)</span>
                        <span class="keyword-tag">நாதன் (Lord)</span>
                        <span class="keyword-tag">வாழ்த்து (Blessing)</span>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="nav-buttons text-center">
                    <button class="btn btn-nav me-3" onclick="prevSong()">
                        <i class="fas fa-chevron-left me-2"></i>Previous
                    </button>
                    <button class="btn btn-nav" onclick="nextSong()">
                        Next<i class="fas fa-chevron-right ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-om me-2"></i>Sowmiya Sagara</h5>
                    <p>Preserving the wisdom of Tamil Siddha tradition</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2025 SRISHTI. All rights reserved.</p>
                    <p><small>Dedicated to Sage Agastiyar and all seekers of truth</small></p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
	
</body>
</html>
<?php
require_once 'layout.php';

ob_start();
?>

<!-- Introduction -->
<div class="intro-section p-4">
    <div class="om-symbol">ॐ</div>
    <h3 class="text-center mb-4">Welcome to Sow<PERSON> Sagaram</h3>
    <div class="section-divider"></div>
    <p class="lead text-center">
        Discover the profound wisdom of <PERSON> through this collection of Tamil Siddha songs - Agastiya <PERSON> Sagaram. Each verse carries the essence of ancient knowledge, spiritual insights, and the path to self-realization.
    </p>
    <p class="text-center">
        In the sacred traditions of Tamil Siddha Yoga, words are never merely semantic containers. Each syllable is a flame, each verse a vessel of condensed tapas. Recognizing this, <PERSON><PERSON><PERSON><PERSON> has brought forth a Dhyāna Urai — meanings revealed through deep meditative states — for these verses of Agastiyar.				
    </p>
    <p class="text-center">	
        His methodology of interpretation is at once devotional, philosophical, philological, and experiential. Rooted in the timeless transmission of nāda, mantra, and jñāna, his insights decode the hidden essence of the Siddha songs with razor-sharp academic clarity and spiritually sensitive depth.
    </p>
    <p class="text-center">	
        Sri <PERSON><PERSON> is a spiritual guide devoted to the Siddha tradition, known for his ability to unveil truths through inner contemplation. His expositions bridge devotion, philosophy, and practical wisdom, offering seekers a clear and transformative path into the timeless teachings of the Tamil Siddhas.
    </p>
</div>

<!-- Song List and Quick Index -->
<div class="row">
    <div class="col-lg-12">
        <h3 class="mb-4"><i class="fas fa-list-ol me-2"></i>Quick Index</h3>
        <div class="quick-index">
            <div class="row g-2">
                <?php for ($i = 1; $i <= 50; $i++): ?>
                <div class="col-3 col-sm-2 col-lg-1">
                    <a href="song-<?php echo $i; ?>.html" class="index-number"><?php echo $i; ?></a>
                </div>
                <?php endfor; ?>
            </div>
            <div class="text-center mt-3">
                <small class="text-muted">Click on any number to go to song</small>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
renderLayout('Tamil Siddha Padalkal', $content, 'home');
?>

RewriteEngine On

# Redirect song-{number}.html to song.php?id={number}
RewriteRule ^song-([0-9]+)\.html$ song.php?id=$1 [L,QSA]

# Optional: Redirect old song.php URLs to pretty URLs
RewriteCond %{THE_REQUEST} \s/+song\.php\?id=([0-9]+) [NC]
RewriteRule ^ song-%1.html? [R=301,L]

# Optional: Set default document
DirectoryIndex index.php

# Optional: Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Optional: Set cache headers for static files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 month"
</IfModule>

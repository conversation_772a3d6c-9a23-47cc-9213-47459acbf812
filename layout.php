<?php
function renderLayout($title, $content, $currentPage = 'home') {
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($title); ?> - Sow<PERSON> Sagaram</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Tamil:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513; /* Saddle Brown - earthy, traditional */
            --secondary-color: #DAA520; /* Golden Rod - representing knowledge */
            --accent-color: #CD853F; /* Peru - warm terracotta */
            --text-primary: #2C1810; /* Dark brown */
            --text-secondary: #5D4E37; /* Dark olive brown */
            --bg-light: #FFF8DC; /* Cornsilk - warm, parchment-like */
            --bg-secondary: #F5F5DC; /* Beige */
            --border-color: #DEB887; /* Burlywood */
        }
        
        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }
        
        .sage-image {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid var(--secondary-color);
            object-fit: cover;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        
        /* Navigation */
        .navbar {
            background: rgba(139, 69, 19, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
        }
        
        .navbar-brand, .nav-link {
            color: white !important;
            font-weight: 500;
        }
        
        .navbar-brand {
            font-size: 1.1rem;
        }
        
        .navbar-toggler {
            border-color: rgba(218, 165, 32, 0.5);
        }
        
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        
        /* Main Content */
        .intro-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
            border: 2px solid var(--border-color);
        }
        
        .song-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            overflow: hidden;
            cursor: pointer;
        }
        
        .song-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.15);
            border-color: var(--secondary-color);
        }
        
        .song-number {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .song-title {
            color: var(--text-primary);
            font-weight: 600;
            margin: 0;
        }
        
        .song-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 0;
        }
        
        /* Song Detail Page */
        .song-detail {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid var(--border-color);
        }
        
        .tamil-text {
            font-family: 'Noto Sans Tamil', serif;
            font-size: 1.3rem;
            line-height: 1.8;
            color: var(--text-primary);
            background: var(--bg-light);
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid var(--secondary-color);
        }
        
        .transliteration {
            font-style: italic;
            color: var(--text-secondary);
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 3px solid var(--accent-color);
        }
        
        .meaning-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .keyword-tag {
            background: var(--secondary-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            margin: 0.2rem;
            display: inline-block;
        }
        
        /* Navigation Buttons */
        .nav-buttons {
            background: var(--bg-secondary);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 2rem;
        }
        
        .btn-nav {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-nav:hover {
            background: var(--accent-color);
            transform: translateY(-1px);
            color: white;
        }
        
        /* Footer */
        .footer {
            background: var(--primary-color);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        /* Decorative Elements */
        .section-divider {
            width: 100px;
            height: 3px;
            background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
            margin: 1.5rem auto;
            border-radius: 2px;
        }
        
        .om-symbol {
            font-size: 2rem;
            color: var(--secondary-color);
            text-align: center;
            margin: 1rem 0;
        }

        /* Quick Index */
        .quick-index {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid var(--border-color);
        }

        .index-number {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
            text-decoration: none;
        }

        .index-number:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
            background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
            color: white;
            text-decoration: none;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 0.9rem;
            }
            
            .intro-section {
                margin: 1rem 0;
                padding: 1.5rem !important;
            }
            
            .song-card {
                margin-bottom: 0.8rem;
            }
            
            .song-number {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
            
            .song-title {
                font-size: 1rem;
            }
            
            .song-subtitle {
                font-size: 0.8rem;
            }
            
            .index-number {
                width: 40px;
                height: 40px;
                font-size: 0.9rem;
            }
            
            .quick-index {
                margin-top: 2rem;
            }

            .sage-image {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <!-- Header/Navigation -->
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container">
            <div class="d-flex align-items-center">
                <img src="agastiya.jpg" 
                     alt="Sage Agastiyar" class="sage-image me-3">
                <a class="navbar-brand flex-grow-1" href="index.php">
                    <span class="d-none d-md-inline">சௌமிய சாகரம் - </span>Sowmiya Sagaram
                </a>
            </div>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link <?php echo $currentPage === 'home' ? 'active' : ''; ?>" href="index.php">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
					<a class="nav-link" href="https://srishaktisumanan.org/"><i class="fas fa-info-circle me-1"></i>About</a>
                    <a class="nav-link" href="https://agastyakulapati.org/%f0%9f%93%9c-revealing-the-hidden-flame-the-methodology-of-analysis-in-the-fivefold-light-of-agastya-panca-kavyam/"><i class="fas fa-info-circle me-1"></i>Methodology</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <?php echo $content; ?>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-om me-2"></i>Sowmiya Sagaram</h5>
                    <p>Preserving the wisdom of Tamil Siddha tradition</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2025 SRISHTI. All rights reserved.</p>
                    <p><small>Dedicated to Sage Agastiyar and all seekers of truth</small></p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php
}
?>
